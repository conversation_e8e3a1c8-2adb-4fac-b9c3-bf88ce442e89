import { DataTable } from '@badeball/cypress-cucumber-preprocessor';
import {
  DataTestSelector,
  deleteFolderRecord,
  deleteSDOsFromRecord,
  Graphql,
} from '../support/helperFunction/caseManagerHelper';

export const caseManagerPage = {
  visit: (): void => {
    cy.visit('/');
    cy.get('body').should(($body) => {
      expect($body.text()).to.match(/Case Management/);
    });
    cy.url().should('include', 'case-manager');
  },

  clickAddButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.AddIconBtn }).first().click(),

  clickCreateNewCaseButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.CreateNewCaseBtn }).click(),

  clickUploadFilesButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.UploadFileBtn }).click(),

  typeCaseName: (caseName: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseNameTextField }).clear();
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseNameTextField }).type(
      caseName
    );
  },

  typeCaseId: (caseId: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseIdTextField }).clear();
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseIdTextField }).type(caseId);
  },

  typeCaseDescription: (caseDescription: string) => {
    cy.getDataIdCy({
      idAlias: DataTestSelector.CaseDescriptionTextField,
    }).clear();
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseDescriptionTextField }).type(
      caseDescription
    );
  },

  selectCaseStatus: (caseStatus: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.StatusList }).click();
    cy.get('[role="listbox"]').find('li').contains(caseStatus).click();
    cy.get('body').type('{esc}');
  },

  clearCaseStatus: () => {
    cy.getDataIdCy({ idAlias: DataTestSelector.StatusList }).within(() => {
      cy.get('.create-case__clear-icon').click();
    });
  },

  selectCaseDate: (date: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.DateSelectorForm }).click();
    cy.get('input[placeholder="MM/DD/YYYY"]').clear();
    cy.get('input[placeholder="MM/DD/YYYY"]').type(date);
  },

  selectTagNames: (tagNames: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.TagList }).click();
    const tags = tagNames.split(',').map((tag) => tag.trim());
    tags.forEach((tag) => {
      cy.getDataIdCy({ idAlias: DataTestSelector.SearchTagInput }).clear();
      cy.getDataIdCy({ idAlias: DataTestSelector.SearchTagInput }).type(tag);
      cy.get('[role="listbox"]').find('li').contains(tag).click();
    });
    cy.get('body').type('{esc}');
  },

  createDefaultCase: (caseName: string) => {
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const year = today.getFullYear();
    const formattedDate = `${month}${day}${year}`;
    caseManagerPage.clickAddButton();
    caseManagerPage.clickCreateNewCaseButton();
    caseManagerPage.verifyModalTitle('Create New Case');
    caseManagerPage.typeCaseName(caseName);
    caseManagerPage.typeCaseId('000-000-000-001');
    caseManagerPage.typeCaseDescription('This is test data');
    caseManagerPage.selectCaseStatus('open');
    caseManagerPage.selectCaseDate(formattedDate);
    caseManagerPage.selectTagNames('Close1');
    caseManagerPage.clickCreateEditCaseButton('save');
  },

  addMultipleTags: (numTags: number) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.TagList }).click();
    for (let i = 0; i < numTags; i++) {
      cy.get('[data-testid^="multi-select-search-option-"]').eq(i).click();
    }
    cy.get('body').type('{esc}');
  },

  clickCreateEditCaseButton: (buttonType: string) =>
    cy.get(`[data-testid="create-edit-case-${buttonType}-button"]`).click(),

  closeSuccessModal: () =>
    cy
      .get('[data-test="data-center-importer-dialog-back-btn"]')
      .contains('Close')
      .click(),

  goToNewCase: () =>
    cy
      .get('[data-test="data-center-importer-dialog-confirm-close-btn"]')
      .contains('Go to New Case')
      .click(),

  verifyModalTitle: (modalName: string) =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.CreateCaseTitle })
      .should('contain.text', modalName),

  verifyTagCount: (numTags: number) =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.NumberOfTagsSelected })
      .then(($el) => {
        const text = $el.text();
        const number = parseInt(text);
        expect(number).to.equal(numTags);
      }),

  verifySuccessModal: () =>
    cy
      .get('[data-test="data-center-importer-dialog-title"]')
      .should('contain.text', 'Case Created Successfully!'),

  getCaseRowByName: (caseName: string) =>
    cy
      .get('[data-testid^="table-row-"]')
      .then(() =>
        cy
          .get(`[data-testid^="table-row-"]`)
          .filter(
            (_index, tr) =>
              Cypress.$(tr)
                .find(`[data-testid="row-case-name"]`)
                .text()
                .trim() === caseName,
            { timeout: 60000 }
          )
      ),

  verifyCaseInTable: (caseName: string) => {
    cy.get('[data-testid^="table-row-"]')
      .should(($rows) =>
        $rows.toArray().some((el) => Cypress.$(el).text().trim() === caseName)
      )
      .then(() => {
        caseManagerPage
          .getCaseRowByName(caseName)
          .should('have.length', 1)
          .and('be.visible');
      });
  },
  openKebabMenuForCase: (caseName: string) => {
    caseManagerPage.getCaseRowByName(caseName).click();
    cy.awaitNetworkResponseCode({ alias: '@fetchDetailPopup', code: 200 });
    caseManagerPage
      .getCaseRowByName(caseName)
      .within(() =>
        cy
          .getDataIdCy({ idAlias: DataTestSelector.SearchTableMenu })
          .click({ force: true })
      );
  },

  selectKebabMenuItem: (menuItemName: string) =>
    cy.get('[role="menu"]').contains(menuItemName).should('be.visible').click(),

  clickConfirmButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ConfirmBtn }).click(),

  typeInConfirmationInput: (text: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.ConfirmationInput }).type(text);
  },

  sortTableColumn: (label: string, orderBy: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${label}` }).as('tableHeading');

    cy.get('@tableHeading').then(($heading) => {
      if (orderBy === 'a-z') {
        if (
          $heading.attr('aria-sort') === 'descending' ||
          !$heading.attr('aria-sort')
        ) {
          cy.get('@tableHeading').click({ scrollBehavior: false });
          cy.get('@tableHeading').trigger('mouseover');
          cy.get('@tableHeading').within(() => {
            cy.getDataIdCy({ idAlias: 'ArrowDropUpIcon' }).should('be.visible');
          });
        }

        cy.wrap($heading).should('have.attr', 'aria-sort', 'ascending');
      } else if (orderBy === 'z-a') {
        if (
          $heading.attr('aria-sort') === 'ascending' ||
          !$heading.attr('aria-sort')
        ) {
          cy.get('@tableHeading').click({ scrollBehavior: false });
          cy.get('@tableHeading').trigger('mouseover');
          cy.get('@tableHeading').within(() => {
            cy.getDataIdCy({ idAlias: 'ArrowDropDownIcon' }).should(
              'be.visible'
            );
          });
        }

        cy.wrap($heading).should('have.attr', 'aria-sort', 'descending');
      }
    });

    cy.assertNoLoading();
  },

  clickFilterButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.FilterBtn }).click(),

  filterCaseName: (filterText: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseNameInput }).clear();
    cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseNameInput }).type(
      filterText
    );
  },

  clickFilterButtonByText: (buttonText: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseFilter })
      .find('button')
      .contains(buttonText)
      .as('filterBtnByText');
    cy.get('@filterBtnByText').click();
    cy.assertNoLoading();
  },

  filterCaseId: (filterText: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseIdInput }).clear();
    cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseIdInput }).type(
      filterText
    );
  },

  filterCaseStatus: (status: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseStatusFilter }).click();
    cy.get('[role="listbox"]').contains(status).click();
  },

  filterCaseTag: (tag: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseTagFilter }).click();
    cy.get('[role="listbox"]')
      .should('be.visible')
      .within(() => {
        cy.get(`[data-testid^="case-tag-option-"]`).contains(tag).click();
      });
    cy.get('body').type('{esc}');
  },

  openCaseByName: (caseName: string) => {
    caseManagerPage.getCaseRowByName(caseName).click();
    cy.assertNoLoading();
  },

  openCaseStatusDropdownInTable: (caseName: string) => {
    caseManagerPage.getCaseRowByName(caseName).within(() => {
      cy.get('[data-testid="case-status-button"]').click();
    });
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseStatusMenu }).should(
      'be.visible'
    );
  },

  selectCaseStatusInDropdown: (status: string) => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseStatusMenu })
      .find(`[data-testid^="case-status-item-"]`)
      .contains(status)
      .click();
    cy.assertNoLoading();
  },

  openCaseStatusDropdownInDetail: () => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseDetailContent }).within(
      () => {
        cy.get('[data-testid^="case-status-button"]').click();
      }
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseStatusMenu }).should(
      'be.visible'
    );
  },

  verifyFilterCaseShown: () => {
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseFilter }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.CaseFilter }).within(() => {
      cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseNameInput }).should(
        'be.visible'
      );
      cy.getDataIdCy({ idAlias: DataTestSelector.FilterCaseIdInput }).should(
        'be.visible'
      );
    });
  },

  verifyCasesFilteredByName: (name: string) => {
    cy.get('tbody tr').should('exist');
    cy.get('tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="table-cell-caseName"] span')
          .should('be.visible')
          .invoke('text')
          .should('match', new RegExp(name, 'i'));
      });
  },

  verifyCasesFilteredByTypeAndValue: (type: string, value: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${type}` }).then(($th) => {
      const columnIndex = $th.index();
      cy.get('[data-testid^="table-row-"]').then(($rows) => {
        cy.wrap($rows).each((row) => {
          cy.wrap(row)
            .find(`.table-cell:nth-child(${columnIndex + 1})`)
            .invoke('text')
            .invoke('trim')
            .should('include', value);
        });
      });
    });
  },

  verifyCaseRemovedFromTable: (caseName: string) =>
    caseManagerPage.getCaseRowByName(caseName).should('not.exist'),

  verifyCaseStatusInTable: (text: string, caseName: string) =>
    caseManagerPage
      .getCaseRowByName(caseName)
      .find('[data-testid="case-status-button"]')
      .should('contain.text', text),

  verifyCursorForLabel: (label: string, cursorType: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${label}` }).as('tableHeading');
    cy.get('@tableHeading').children().trigger('mouseover');
    cy.get('@tableHeading').children().should('have.css', 'cursor', cursorType);
  },

  verifyLabelNotClickable: (label: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${label}` }).as('tableHeading');
    cy.get('@tableHeading').click();
    cy.get('@tableHeading').should('not.have.attr', 'aria-sort');
  },

  deleteFolderAndSDOsIfExist: (
    folderName?: string,
    folderNamesDataTable?: DataTable
  ) => {
    const names: string[] = folderName
      ? [folderName]
      : folderNamesDataTable?.hashes().map((r) => r.folderName) || [];

    if (names.length === 0) {
      cy.log('No folder names provided to delete.');
      return;
    }

    const variables = {
      id: '0a8a0348-7ddf-4dd8-9238-8c9e6b8a21fb',
      limit: 500,
      offset: 0,
    };

    cy.Graphql(Graphql.getChildFoldersByParentFolderId, variables).then(
      (res) => {
        const records = res.body?.data?.folder?.childFolders?.records || [];

        for (const targetFolderName of names) {
          const matchingRecords = records.filter(
            (record: { name: string }) => record.name === targetFolderName
          );
          if (matchingRecords.length === 0) {
            cy.log(`No folder found with name: ${targetFolderName}`);
            continue;
          }

          cy.log(
            `Found ${matchingRecords.length} folder(s) with name: ${targetFolderName}`
          );

          for (const record of matchingRecords) {
            deleteSDOsFromRecord(record, targetFolderName);
            deleteFolderRecord(record, targetFolderName);
          }
        }
      }
    );
  },
  verifySuccessModalNotDisplayed: () => {
    cy.get('[data-test="data-center-importer-dialog-title"]').should(
      'not.exist'
    );
    cy.get('body').should('not.contain.text', 'Case Created Successfully!');
  },
  verifyBackdropOverlay: () => {
    cy.get('[data-testid="move-file-dialog"]')
      .find('> [aria-hidden="true"]')
      .as('backdrop')
      .should('exist')
      .and('have.css', 'opacity', '1');
  },
  verifyOverlayBackground: () => {
    cy.get('@backdrop').should(
      'have.css',
      'background-color',
      'rgba(0, 0, 0, 0.5)'
    );
  },
};
