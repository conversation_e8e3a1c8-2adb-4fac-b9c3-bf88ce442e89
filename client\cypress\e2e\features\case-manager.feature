Feature: Case Manager

  Background: The user is on Case Management screen
    Given The user is on Case Management screen

  @e2e @case-manager
  Scenario: User can not create a case with more than 10 tags
    When The user presses the plus icon button
    And The user selects Create New Case
    Then The user sees the "Create New Case" modal
    When The user inputs case name "Cypress Test Tags"
    And The user attempts to add 11 tags
    Then The total number of tags selected is 10

  @e2e @case-manager
  Scenario: Case creation failure shows only error message, not success modal
    When The user presses the plus icon button
    And The user selects Create New Case
    Then The user sees the "Create New Case" modal
    When The user inputs case name "Cypress Test Case Failure"
    And The user inputs case id "000-000-000-999"
    And The user inputs case description "This case should fail to create"
    And The user selects case status "open"
    And The user selects case date "12252024"
    And The user selects tag name "Close1"
    And The user simulates case creation failure due to permissions
    And The user clicks on the "save" case button
    Then The user sees notification "Case Creation Failed"
    And The user should not see the success case modal
    And The user sees the "Create New Case" modal

  @e2e @case-manager
  Scenario: User create a case when in offine mode
    Given The user deletes the following cases if exist: "Cypress Test Case Offline"
    And The user is offline
    When The user creates a default case name "Cypress Test Case Offline"
    Then The user sees notification "Case Creation Failed"

  @e2e @case-manager
  Scenario: User create a case and not open it
    Given The user deletes the following cases if exist: "Cypress Test Case 1"
    When The user creates a default case name "Cypress Test Case 1"
    Then The user sees notification "Case Created Successfully"
    And The user sees the success case modal
    When The user closes the success case modal
    And The user sees case "Cypress Test Case 1" in case table list

  @e2e @case-manager
  Scenario: User creates a case and navigates to case details
    Given The user deletes the following cases if exist: "Cypress Test Case 2"
    When The user creates a default case name "Cypress Test Case 2"
    Then The user sees notification "Case Created Successfully"
    And The user sees the success case modal
    When The user goes to the created case
    And The case details for "Cypress Test Case 2" are displayed

  @e2e @case-manager @upload-file
  Scenario: User uploads a file in Case Manager page
    Given The user deletes the following cases if exist: "Cypress Test Case Upload 1"
    When The user creates a default case name "Cypress Test Case Upload 1"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When The user presses the plus icon button
    And The user selects Upload Files
    And The user uploads files "lucy.mp4"
    And The user changes the location to "Cypress Test Case Upload 1"
    Then The user should see the target location case is "Cypress Test Case Upload 1"
    When The user clicks the Import button
    Then The user sees case "Cypress Test Case Upload 1" in case table list
    # When The user goes to case detail "Cypress Test Case Upload 1"
    # Then The user sees file "lucy.mp4" in file table list

  @e2e @case-manager
  Scenario: Edit case (Kebab menu)
    Given The user deletes the following cases if exist: "Cypress Test Case Edit Kebab"
    And The user deletes the following cases if exist: "Cypress Test Case Edit Kebab 2"
    Given The user creates a default case name "Cypress Test Case Edit Kebab"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    Then The user sees case "Cypress Test Case Edit Kebab" in case table list
    When The user opens kebab menu for case "Cypress Test Case Edit Kebab"
    And The user selects "Edit Case" from the kebab menu
    And The user sees the "Edit Case" modal
    And Case input fields should be:
      | fieldName   | value                        |
      | caseName    | Cypress Test Case Edit Kebab |
      | caseId      | 000-000-000-001              |
      | description | This is test data            |
    When The user inputs case name "Cypress Test Case Edit Kebab 2"
    And The user inputs case id "000-000-000-002"
    And The user inputs case description "Cypress Edit"
    And The user clicks on the "save" case button
    And The user sees case "Cypress Test Case Edit Kebab 2" in case table list

  @e2e @case-manager
  Scenario: Edit case (Case Details)
    Given The user deletes the following cases if exist: "Cypress Test Case Edit Detail"
    And The user deletes the following cases if exist: "Cypress Test Case Edit Detail 2"
    Given The user creates a default case name "Cypress Test Case Edit Detail"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    Then The user sees case "Cypress Test Case Edit Detail" in case table list
    When The user opens kebab menu for case "Cypress Test Case Edit Detail"
    And The user selects "Edit Case" from the kebab menu
    And The user sees the "Edit Case" modal
    And Case input fields should be:
      | fieldName   | value                         |
      | caseName    | Cypress Test Case Edit Detail |
      | caseId      | 000-000-000-001               |
      | description | This is test data             |
    When The user inputs case name "Cypress Test Case Edit Detail 2"
    And The user inputs case id "000-000-000-003"
    And The user inputs case description "Cypress Edit"
    And The user clicks on the "save" case button
    And The user sees case "Cypress Test Case Edit Detail 2" in case table list

  @e2e @case-manager
  Scenario: Delete case
    Given The user deletes the following cases if exist: "Cypress Test Case Delete"
    Given The user creates a default case name "Cypress Test Case Delete"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When The user opens kebab menu for case "Cypress Test Case Delete"
    When The user selects "Delete" from the kebab menu
    And The user presses the confirm button
    Then The user sees notification "Case Deletion in progress. It will be removed shortly."
    And Case "Cypress Test Case Delete" is removed from the table

  @e2e @case-manager
  Scenario Outline: Sort case table by <label> <sortedBy>
    When The user hovers over the '<label>' label and see cursor "pointer"
    Given The user presses the '<label>' sort button to sort by '<sortedBy>'
    Then '<label>' is sorted by '<sortedBy>'

    Examples:
      | label    | sortedBy |
      | caseName | z-a      |
      | caseDate | a-z      |
      | caseDate | z-a      |

  @e2e @case-manager
  Scenario Outline: Verify user is not able to sort by <label>
    When The user hovers over the '<label>' label and see cursor "default"
    And The label '<label>' should not be clickable

    Examples:
      | label    |
      | caseId   |
      | statusId |

  @e2e @case-manager
  Scenario: Filter case table
    Given The user deletes the following cases if exist: "Cypress Test Filtered"
    Given The user creates a default case name "Cypress Test Filtered"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When The user presses the filter button
    Then The filter drawer should be shown
    When The user enters "Filtered" in the case name filter
    And The user clicks the "Apply" filter button
    And The cases should be filtered by "caseName" "Filtered"
    When The user presses the filter button
    And The user enters "000" in the case id filter
    And The user clicks the "Apply" filter button
    And The cases should be filtered by "caseId" "000"
    When The user presses the filter button
    And The user selects status "open" in the filter
    And The user clicks the "Apply" filter button
    And The cases should be filtered by "statusId" "open"
    When The user presses the filter button
    And The user selects tag "Close1" in the filter
    And The user clicks the "Apply" filter button
    When The user opens case "Cypress Test Filtered"
    Then The user sees "Close1" tag in details

  @e2e @case-manager @upload-file
  Scenario: Sort file table
    Given The user deletes the following cases if exist: "Cypress Test sort table"
    Given The user creates a default case name "Cypress Test sort table"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    And The user clicks Add Files in case detail
    And The user adds files "upload_test.mp4"
    When The user clicks the Import button
    # Then The user sees notification "uploaded successfully"
    Then The user sees file "lucy.mp4, upload_test.mp4 " in file table list
    When The user presses the 'fileName' sort button to sort by 'z-a'
    Then 'fileName' is sorted by 'z-a'
    When The user presses the 'fileName' sort button to sort by 'a-z'
    Then 'fileName' is sorted by 'a-z'
    When The user presses the 'createdTime' sort button to sort by 'a-z'
    Then 'createdTime' is sorted by 'a-z'
    When The user presses the 'createdTime' sort button to sort by 'z-a'
    Then 'createdTime' is sorted by 'z-a'

  @e2e @case-manager
  Scenario: Show black overlay when the "Move to Case" modal is open
    Given The user deletes the following cases if exist:
      | caseName                       |
      | Cypress Test Overlay Move File |
    Given The user creates a default case name "Cypress Test Overlay Move File"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user sees case table list is empty
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    Then The user sees file "lucy.mp4" in file table list
    When The user opens kebab menu for file "lucy.mp4"
    And The user selects "Move" from the kebab menu
    Then A backdrop overlay should be visible
    And The overlay should have a semi-transparent black background

  @e2e @case-manager
  Scenario: Move file
    Given The user deletes the following cases if exist:
      | caseName                    |
      | 000 Cypress Test Moved File |
      | Cypress Test Move File      |
    Given The user creates a default case name "000 Cypress Test Moved File"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    Given The user creates a default case name "Cypress Test Move File"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    When The user clicks the Import button
    Then The user sees file "lucy.mp4" in file table list
    When The user opens kebab menu for file "lucy.mp4"
    And The user selects "Move" from the kebab menu
    And The user selects the target case "000 Cypress Test Moved File"
    Then The user sees file "lucy.mp4" in file table list
    Given The user is on Case Management screen
    When The user opens case "Cypress Test Move File"
    And The user clicks View Case Details button
    Then The user sees case table list is empty

  @e2e @case-manager @file-upload
  Scenario: Delete a file in a case
    Given The user deletes the following cases if exist: "Cypress Test Case Upload Delete 1"
    When The user creates a default case name "Cypress Test Case Upload Delete 1"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user sees case table list is empty
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    When The user clicks the Import button
    Then The user sees file "lucy.mp4" in file table list
    When The user opens kebab menu for file "lucy.mp4"
    And The user selects "Delete" from the kebab menu
    Then The user types "delete-file" in the confirmation input
    And The user presses the confirm button
    Then The user sees notification "File Deletion in progress. It will be removed shortly."

  @e2e @case-manager
  Scenario: Update case status in case table
    Given The user deletes the following cases if exist: "Cypress Test Update status table"
    Given The user creates a default case name "Cypress Test Update status table"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When The user opens case "Cypress Test Update status table"
    When The user opens case status dropdown for "Cypress Test Update status table" in case table
    When The user selects status "Close2" in the dropdown
    Then The user sees "Close2" status for "Cypress Test Update status table" in case table
    Then The user sees "Close2" status in details

  @e2e @case-manager
  Scenario: Update case status in case details
    Given The user deletes the following cases if exist: "Cypress Test Update status detail"
    Given The user creates a default case name "Cypress Test Update status detail"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When The user opens case "Cypress Test Update status detail"
    When The user opens case status dropdown in case detail
    When The user selects status "Close2" in the dropdown
    Then The user sees "Close2" status in details
    Then The user sees "Close2" status for "Cypress Test Update status detail" in case table

  @e2e @case-manager @file-upload
  Scenario: Upload a file in an empty case details page
    Given The user deletes the following cases if exist: "Test Upload a file in an empty case"
    Given The user creates a default case name "Test Upload a file in an empty case"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user sees case table list is empty
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    When The user clicks the Import button
    Then The user sees file "lucy.mp4" in file table list

  @e2e @case-manager @file-upload
  Scenario: Add files to case (Kebab menu)
    Given The user deletes the following cases if exist: "Test Upload a file to an empty case"
    Given The user creates a default case name "Test Upload a file to an empty case"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    Then The user reloads page
    When The user opens case "Test Upload a file to an empty case"
    When The user opens kebab menu for case "Test Upload a file to an empty case"
    And The user selects "Add Files" from the kebab menu
    Then The user clicks add file button when using Kebab menu
    And The user uploads files "lucy.mp4"
    When The user clicks the Import button
    When The user goes to case detail "Test Upload a file to an empty case"
    Then The user sees file "lucy.mp4" in file table list

  @e2e @case-manager @keyboard-event
  Scenario: Keyboard navigation between cases
    When The user presses "Tab" key on the table
    Then The "1st" item in the table is selected
    When The user presses "ArrowDown" key 1 time(s)
    Then The "2nd" item in the table is selected
    When The user presses "ArrowUp" key 1 time(s)
    Then The "1st" item in the table is selected

  @e2e @case-manager @keyboard-event
  Scenario: Keyboard navigation between files in a case detail
    Given The user deletes the following cases if exist: "Cypress Test Keyboard Navigation"
    Given The user creates a default case name "Cypress Test Keyboard Navigation"
    Then The user sees notification "Case Created Successfully"
    When The user goes to the created case
    Then The user clicks add file button in case table list
    And The user clicks Browses Files in case detail
    And The user uploads files "lucy.mp4"
    And The user clicks Add Files in case detail
    And The user adds files "upload_test.mp4"
    When The user clicks the Import button
    Then The user sees file "lucy.mp4, upload_test.mp4 " in file table list
    When The user presses "Tab" key on the table
    Then The "1st" item in the table is selected
    When The user presses "ArrowDown" key 1 time(s)
    Then The "2nd" item in the table is selected
    When The user presses "ArrowUp" key 1 time(s)
    Then The "1st" item in the table is selected

  @e2e @case-manager
  Scenario: Delete created cases after run
    Given The user deletes the following cases if exist:
      | caseName                            |
      | Cypress Test Case 1                 |
      | Cypress Test Case 2                 |
      | Cypress Test Case Upload 1          |
      | Cypress Test Case Edit Kebab        |
      | Cypress Test Case Edit Kebab 2      |
      | Cypress Test Case Edit Detail       |
      | Cypress Test Case Edit Detail 2     |
      | Cypress Test Case Delete            |
      | Cypress Test Filtered               |
      |         000 Cypress Test Moved File |
      | Cypress Test Move File              |
      | Cypress Test sort table             |
      | Cypress Test Move File 1            |
      | Cypress Test Case Upload Delete 1   |
      | Cypress Test Update status table    |
      | Cypress Test Update status detail   |
      | Test Upload a file in an empty case |
      | Test Upload a file to an empty case |
      | Cypress Test Keyboard Navigation    |

  @e2e @case-manager
  Scenario: Delete SDOs and folders after run
    Given The user deletes the following SDOs and folders if exist:
      | folderName                          |
      | Cypress Test Case 1                 |
      | Cypress Test Case 2                 |
      | Cypress Test Case Upload 1          |
      | Cypress Test Case Edit Kebab        |
      | Cypress Test Case Edit Kebab 2      |
      | Cypress Test Case Edit Detail       |
      | Cypress Test Case Edit Detail 2     |
      | Cypress Test Case Delete            |
      | Cypress Test Filtered               |
      |         000 Cypress Test Moved File |
      | Cypress Test Move File              |
      | Cypress Test sort table             |
      | Cypress Test Move File 1            |
      | Cypress Test Case Upload Delete 1   |
      | Cypress Test Update status table    |
      | Cypress Test Update status detail   |
      | Test Upload a file in an empty case |
      | Test Upload a file to an empty case |
      | Cypress Test Keyboard Navigation    |
